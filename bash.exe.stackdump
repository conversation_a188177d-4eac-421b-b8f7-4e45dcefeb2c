Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF991360000 ntdll.dll
7FF990F50000 KERNEL32.DLL
7FF98E6D0000 KERNELBASE.dll
7FF98FF10000 USER32.dll
7FF98EF90000 win32u.dll
7FF9900E0000 GDI32.dll
7FF98EFC0000 gdi32full.dll
7FF98EB50000 msvcp_win.dll
7FF98EE40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF990430000 advapi32.dll
7FF9902D0000 msvcrt.dll
7FF990380000 sechost.dll
7FF98FDD0000 RPCRT4.dll
7FF98DB40000 CRYPTBASE.DLL
7FF98E630000 bcryptPrimitives.dll
7FF98F460000 IMM32.DLL
