﻿<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0-desktop</TargetFramework>

    <OutputType>WinExe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>PreviewFramework.DevToolsApp</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.PreviewFramework.DevToolsApp</ApplicationId>
    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <!-- Package Publisher -->
    <ApplicationPublisher>bret</ApplicationPublisher>
    <!-- Package Description -->
    <Description>PreviewFramework.DevToolsApp powered by Uno Platform.</Description>

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Lottie;
      Hosting;
      Toolkit;
      Logging;
      Mvvm;
      Configuration;
      Navigation;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
  </PropertyGroup>

  <ItemGroup>
    <UnoIcon Include="Assets\Icons\icon.svg" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PreviewFramework.Tooling\PreviewFramework.Tooling.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Workspaces.MSBuild" Version="4.14.0" />
    <PackageReference Include="Microsoft.Build.Locator" Version="1.9.1" />
  </ItemGroup>

</Project>
